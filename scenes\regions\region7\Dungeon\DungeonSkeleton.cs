using Godot;
using System;

public partial class DungeonSkeleton : CharacterBody2D
{
	[Export] public int MaxHealth { get; set; } = 5;
	[Export] public int Damage { get; set; } = 1;
	[Export] public float MovementSpeed { get; set; } = 60.0f;
	[Export] public float DetectionRange { get; set; } = 80.0f;
	[Export] public float AttackRange { get; set; } = 18.0f;
	[Export] public float OptimalCombatDistance { get; set; } = 16.0f;
	[Export] public float WanderRange { get; set; } = 32.0f;
	[Export] public float WanderWaitTimeMin { get; set; } = 3.0f;
	[Export] public float WanderWaitTimeMax { get; set; } = 5.0f;
	[Export] public float AttackCooldown { get; set; } = 1.0f;
	[Export] public float AttackWindupTime { get; set; } = 0.5f;

	[Signal] public delegate void DungeonSkeletonKilledEventHandler(Vector2I spawnTilePosition);

	private int _currentHealth;
	private Vector2I _spawnTilePosition;
	private Vector2 _spawnWorldPosition;
	private Node2D _currentTarget;
	private SkeletonState _currentState = SkeletonState.Wandering;
	private string _currentDirection = "down";
	
	private Sprite2D _sprite;
	private AnimationPlayer _animationPlayer;
	private Area2D _detectionArea;
	private ProgressBar _healthBar;
	private Timer _wanderTimer;
	private Timer _attackTimer;
	private Timer _attackWindupTimer;
	
	private bool _isAttacking = false;
	private bool _isWindingUp = false;
	private Vector2 _wanderTarget;
	private bool _hasWanderTarget = false;

	public enum SkeletonState
	{
		Wandering,
		Following,
		Attacking,
		Returning
	}

	public override void _Ready()
	{
		_sprite = GetNode<Sprite2D>("Sprite2D");
		_animationPlayer = GetNode<AnimationPlayer>("AnimationPlayer");
		_detectionArea = GetNode<Area2D>("DetectionArea");
		_healthBar = GetNode<ProgressBar>("ProgressBar");
		
		_wanderTimer = GetNode<Timer>("WanderTimer");
		_attackTimer = GetNode<Timer>("AttackTimer");
		_attackWindupTimer = GetNode<Timer>("AttackWindupTimer");

		_currentHealth = MaxHealth;
		UpdateHealthBar();

		if (_detectionArea != null)
		{
			_detectionArea.CollisionMask = 4;
			_detectionArea.AreaEntered += OnDetectionAreaEntered;
			_detectionArea.AreaExited += OnDetectionAreaExited;
		}

		if (_wanderTimer != null)
		{
			_wanderTimer.Timeout += OnWanderTimer;
		}

		if (_attackTimer != null)
		{
			_attackTimer.Timeout += OnAttackTimer;
		}

		if (_attackWindupTimer != null)
		{
			_attackWindupTimer.Timeout += OnAttackWindupTimer;
		}

		if (_animationPlayer != null)
		{
			_animationPlayer.AnimationFinished += OnHitAnimationFinished;
		}

		SetupDetectionArea();

		// Connect to sword usage signal
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed += OnSwordUsed;
		}

		GD.Print("DungeonSkeleton: Ready completed");
	}

	public override void _ExitTree()
	{
		if (CommonSignals.Instance != null)
		{
			CommonSignals.Instance.SwordUsed -= OnSwordUsed;
		}
		base._ExitTree();
	}

	public void Initialize(Vector2I spawnTilePosition, int currentHealth, int maxHealth, int damage)
	{
		_spawnTilePosition = spawnTilePosition;
		_spawnWorldPosition = new Vector2(spawnTilePosition.X * 16, spawnTilePosition.Y * 16);
		GlobalPosition = _spawnWorldPosition;

		MaxHealth = maxHealth;
		_currentHealth = currentHealth;
		Damage = damage;

		UpdateHealthBar();

		// Start with default animation
		PlayAnimation(_currentDirection, "idle");

		GD.Print($"DungeonSkeleton: Initialized at tile {_spawnTilePosition} with {_currentHealth}/{MaxHealth} HP");
	}

	private void SetupDetectionArea()
	{
		if (_detectionArea != null)
		{
			var circleShape = new CircleShape2D();
			circleShape.Radius = DetectionRange;
			
			var collisionShape = _detectionArea.GetNode<CollisionShape2D>("CollisionShape2D");
			if (collisionShape != null)
			{
				collisionShape.Shape = circleShape;
			}
		}
	}

	public override void _PhysicsProcess(double delta)
	{
		HandleStateMachine(delta);
		MoveAndSlide();
	}

	private void HandleStateMachine(double delta)
	{
		switch (_currentState)
		{
			case SkeletonState.Wandering:
				HandleWandering(delta);
				break;
			case SkeletonState.Following:
				HandleFollowing(delta);
				break;
			case SkeletonState.Attacking:
				HandleAttacking(delta);
				break;
			case SkeletonState.Returning:
				HandleReturning(delta);
				break;
		}
	}

	private void HandleWandering(double delta)
	{
		if (!_hasWanderTarget)
		{
			if (_wanderTimer.IsStopped())
			{
				float waitTime = (float)GD.RandRange(WanderWaitTimeMin, WanderWaitTimeMax);
				_wanderTimer.WaitTime = waitTime;
				_wanderTimer.Start();
			}
			return;
		}

		Vector2 direction = (_wanderTarget - GlobalPosition).Normalized();
		Velocity = direction * MovementSpeed;
		UpdateDirectionAndAnimation(direction, "move");

		if (GlobalPosition.DistanceTo(_wanderTarget) < 8.0f)
		{
			_hasWanderTarget = false;
			Velocity = Vector2.Zero;
			PlayIdleAnimation();
		}
	}

	private void HandleFollowing(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(SkeletonState.Wandering);
			return;
		}

		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		
		if (distanceToTarget <= AttackRange && _attackTimer.IsStopped())
		{
			ChangeState(SkeletonState.Attacking);
			return;
		}

		if (distanceToTarget > OptimalCombatDistance)
		{
			Vector2 direction = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
			Vector2 targetPosition = _currentTarget.GlobalPosition - direction * OptimalCombatDistance;
			
			Vector2 moveDirection = (targetPosition - GlobalPosition).Normalized();
			Velocity = moveDirection * MovementSpeed;
			UpdateDirectionAndAnimation(moveDirection, "move");
		}
		else
		{
			Velocity = Vector2.Zero;
			PlayIdleAnimation();
		}
	}

	private void HandleAttacking(double delta)
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget))
		{
			ChangeState(SkeletonState.Wandering);
			return;
		}

		float distanceToTarget = GlobalPosition.DistanceTo(_currentTarget.GlobalPosition);
		if (distanceToTarget > AttackRange)
		{
			ChangeState(SkeletonState.Following);
			return;
		}

		if (!_isWindingUp && !_isAttacking)
		{
			StartAttackWindup();
		}

		Velocity = Vector2.Zero;
	}

	private void HandleReturning(double delta)
	{
		Vector2 direction = (_spawnWorldPosition - GlobalPosition).Normalized();
		Velocity = direction * MovementSpeed;
		UpdateDirectionAndAnimation(direction, "move");

		if (GlobalPosition.DistanceTo(_spawnWorldPosition) < 8.0f)
		{
			ChangeState(SkeletonState.Wandering);
		}
	}

	private void ChangeState(SkeletonState newState)
	{
		_currentState = newState;
		
		switch (newState)
		{
			case SkeletonState.Wandering:
				StartWandering();
				break;
			case SkeletonState.Following:
				break;
			case SkeletonState.Attacking:
				break;
			case SkeletonState.Returning:
				break;
		}
	}

	private void StartWandering()
	{
		_hasWanderTarget = false;
		Velocity = Vector2.Zero;
		PlayIdleAnimation();
		
		if (_wanderTimer != null && _wanderTimer.IsStopped())
		{
			float waitTime = (float)GD.RandRange(WanderWaitTimeMin, WanderWaitTimeMax);
			_wanderTimer.WaitTime = waitTime;
			_wanderTimer.Start();
		}
	}

	private void StartAttackWindup()
	{
		_isWindingUp = true;
		_attackWindupTimer.WaitTime = AttackWindupTime;
		_attackWindupTimer.Start();
	}

	private void ExecuteAttack()
	{
		if (_currentTarget == null || !IsInstanceValid(_currentTarget)) return;

		Vector2 attackDirection = (_currentTarget.GlobalPosition - GlobalPosition).Normalized();
		UpdateDirectionAndAnimation(attackDirection, "attack");

		if (_currentTarget is DungeonPlayerController dungeonPlayer)
		{
			dungeonPlayer.TakeDamage(Damage);
			GD.Print($"DungeonSkeleton attacked player for {Damage} damage!");
		}

		_attackTimer.WaitTime = AttackCooldown;
		_attackTimer.Start();
	}

	private void UpdateDirectionAndAnimation(Vector2 direction, string animationType)
	{
		string newDirection = GetDirectionFromVector(direction);

		if (newDirection != _currentDirection)
		{
			_currentDirection = newDirection;
		}

		PlayAnimation(_currentDirection, animationType);
	}

	private string GetDirectionFromVector(Vector2 direction)
	{
		if (Mathf.Abs(direction.X) > Mathf.Abs(direction.Y))
		{
			return direction.X > 0 ? "right" : "left";
		}
		else
		{
			return direction.Y > 0 ? "down" : "up";
		}
	}

	private void PlayAnimation(string direction, string animationType)
	{
		if (_animationPlayer == null) return;

		string animationName = "";
		switch (animationType)
		{
			case "idle":
				animationName = "Idle" + char.ToUpper(direction[0]) + direction.Substring(1);
				break;
			case "move":
				animationName = "Move" + char.ToUpper(direction[0]) + direction.Substring(1);
				break;
			case "attack":
				animationName = "Attack" + char.ToUpper(direction[0]) + direction.Substring(1);
				break;
		}

		if (_animationPlayer.HasAnimation(animationName))
		{
			_animationPlayer.Play(animationName);
		}
		else
		{
			GD.PrintErr($"DungeonSkeleton: Animation '{animationName}' not found");
		}
	}

	private void PlayIdleAnimation()
	{
		PlayAnimation(_currentDirection, "idle");
	}

	public void TakeDamage(int damage)
	{
		_currentHealth -= damage;
		_currentHealth = Math.Max(0, _currentHealth);

		UpdateHealthBar();
		PlayHitAnimation();

		var dungeonManager = GetNode<DungeonManager>("/root/Dungeon/DungeonManager");
		dungeonManager?.UpdateEnemyHealth(_spawnTilePosition, _currentHealth);

		GD.Print($"DungeonSkeleton took {damage} damage! Health: {_currentHealth}/{MaxHealth}");

		if (_currentHealth <= 0)
		{
			Die();
		}
	}

	private void PlayHitAnimation()
	{
		string hitAnimationName = "GotHit" + char.ToUpper(_currentDirection[0]) + _currentDirection.Substring(1);
		if (_animationPlayer != null && _animationPlayer.HasAnimation(hitAnimationName))
		{
			_animationPlayer.Play(hitAnimationName);
		}
	}

	private void OnHitAnimationFinished(StringName animationName)
	{
		if (animationName.ToString().StartsWith("GotHit"))
		{
			PlayIdleAnimation();
		}
		else if (animationName.ToString().StartsWith("Attack"))
		{
			PlayIdleAnimation();
		}
	}

	private void Die()
	{
		DropGoldCoin();
		EmitSignal(SignalName.DungeonSkeletonKilled, _spawnTilePosition);
		QueueFree();
	}

	private void DropGoldCoin()
	{
		DroppedResource.SpawnCoin(GlobalPosition, 1);
		GD.Print("DungeonSkeleton: Dropped 1 gold coin");
	}

	private void UpdateHealthBar()
	{
		if (_healthBar != null)
		{
			_healthBar.SetProgress((float)_currentHealth / MaxHealth);
			_healthBar.Visible = _currentHealth < MaxHealth;
		}
	}

	private void OnDetectionAreaEntered(Area2D area)
	{
		if (area.Name == "PlayerDetector")
		{
			var parent = area.GetParent();
			if (parent is DungeonPlayerController dungeonPlayer)
			{
				_currentTarget = dungeonPlayer;
				ChangeState(SkeletonState.Following);
				GD.Print("DungeonSkeleton: Player entered detection range, starting to follow");
			}
		}
	}

	private void OnDetectionAreaExited(Area2D area)
	{
		if (area.Name == "PlayerDetector" && _currentTarget != null)
		{
			var parent = area.GetParent();
			if (parent == _currentTarget)
			{
				_currentTarget = null;
				ChangeState(SkeletonState.Returning);
				GD.Print("DungeonSkeleton: Player left detection range, returning to spawn");
			}
		}
	}

	private void OnWanderTimer()
	{
		if (_currentState == SkeletonState.Wandering && !_hasWanderTarget)
		{
			Vector2 randomOffset = new Vector2(
				(float)GD.RandRange(-WanderRange, WanderRange),
				(float)GD.RandRange(-WanderRange, WanderRange)
			);

			_wanderTarget = _spawnWorldPosition + randomOffset;
			_hasWanderTarget = true;
			GD.Print($"DungeonSkeleton: New wander target set to {_wanderTarget}");
		}
	}

	private void OnAttackTimer()
	{
		_isAttacking = false;
	}

	private void OnAttackWindupTimer()
	{
		_isWindingUp = false;
		_isAttacking = true;
		ExecuteAttack();
	}

	private void OnSwordUsed(Vector2I tilePosition, Vector2 playerPosition, Vector2 attackDirection)
	{
		if (IsInSwordAttackArc(playerPosition, attackDirection))
		{
			int swordLevel = GameSaveData.Instance.PlayerStats.ToolLevels.TryGetValue(ToolType.Sword, out int level) ? level : 1;
			int damage = swordLevel + 1;
			TakeDamage(damage);
			GD.Print($"DungeonSkeleton hit by sword for {damage} damage!");
		}
	}

	private bool IsInSwordAttackArc(Vector2 playerPosition, Vector2 attackDirection)
	{
		Vector2 toEnemy = GlobalPosition - playerPosition;
		float distanceToEnemy = toEnemy.Length();

		if (distanceToEnemy > 32.0f) return false;

		Vector2 toEnemyNormalized = toEnemy.Normalized();
		float dotProduct = attackDirection.Dot(toEnemyNormalized);

		return dotProduct > 0.0f;
	}
}
